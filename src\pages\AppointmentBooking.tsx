import React, { useState } from 'react';
import { Doctor, AppointmentFormData } from '../types';
import {
  useGetTimeSlotsQuery,
  useGetAppointmentsQuery,
  useCreateAppointmentMutation,
  useUpdateTimeSlotMutation,
} from '../store/apiSlice';
import TimeSlotPicker from '../components/TimeSlotPicker';
import AppointmentForm from '../components/AppointmentForm';
import AppointmentsTable from '../components/AppointmentsTable';

interface AppointmentBookingProps {
  doctor: Doctor;
}

const AppointmentBooking: React.FC<AppointmentBookingProps> = ({ doctor }) => {
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const { data: timeSlots = [], isLoading: timeSlotsLoading } = useGetTimeSlotsQuery({
    doctorId: doctor.id,
  });

  const { data: appointments = [], isLoading: appointmentsLoading } = useGetAppointmentsQuery(doctor.id);

  const [createAppointment, { isLoading: isCreatingAppointment }] = useCreateAppointmentMutation();
  const [updateTimeSlot] = useUpdateTimeSlotMutation();

  const handleDateChange = (date: string) => {
    setSelectedDate(date);
    setSelectedTime(''); // Reset time when date changes
  };

  const handleTimeChange = (time: string) => {
    setSelectedTime(time);
  };

  const handleAppointmentSubmit = async (formData: AppointmentFormData) => {
    try {
      // Find the time slot to update
      const timeSlot = timeSlots.find(
        slot => slot.date === selectedDate && slot.time === selectedTime
      );

      if (!timeSlot) {
        alert('Selected time slot not found');
        return;
      }

      // Create the appointment
      await createAppointment({
        ...formData,
        doctorId: doctor.id,
        date: selectedDate,
        time: selectedTime,
      }).unwrap();

      // Update the time slot to mark it as booked
      await updateTimeSlot({
        id: timeSlot.id,
        isBooked: true,
      }).unwrap();

      // Show success message
      setShowSuccessMessage(true);
      setSelectedDate('');
      setSelectedTime('');

      // Hide success message after 5 seconds
      setTimeout(() => {
        setShowSuccessMessage(false);
      }, 5000);

    } catch (error) {
      console.error('Error creating appointment:', error);
      alert('Failed to book appointment. Please try again.');
    }
  };

  return (
    <div className="space-y-8">
      {/* Doctor Info Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4">
          <img
            src={doctor.image}
            alt={doctor.name}
            className="w-20 h-20 rounded-full object-cover"
          />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{doctor.name}</h2>
            <p className="text-lg text-gray-600">{doctor.specialty}</p>
            <div className="flex items-center space-x-4 mt-2">
              <span className="text-sm text-gray-500">{doctor.experience}</span>
              <div className="flex items-center">
                <span className="text-yellow-400">★</span>
                <span className="text-sm text-gray-600 ml-1">{doctor.rating}</span>
              </div>
              <span className="text-lg font-semibold text-primary-600">
                ${doctor.consultationFee}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                Appointment Booked Successfully!
              </h3>
              <p className="text-sm text-green-700 mt-1">
                Your appointment has been confirmed. You will receive a confirmation email shortly.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Booking Form */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Book an Appointment
            </h3>
            
            {timeSlotsLoading ? (
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="grid grid-cols-3 gap-2">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-10 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            ) : (
              <TimeSlotPicker
                timeSlots={timeSlots}
                selectedDate={selectedDate}
                selectedTime={selectedTime}
                onDateChange={handleDateChange}
                onTimeChange={handleTimeChange}
              />
            )}
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Patient Information
            </h3>
            <AppointmentForm
              onSubmit={handleAppointmentSubmit}
              selectedDate={selectedDate}
              selectedTime={selectedTime}
              isLoading={isCreatingAppointment}
            />
          </div>
        </div>

        {/* Appointments Table */}
        <div>
          <AppointmentsTable
            appointments={appointments}
            isLoading={appointmentsLoading}
          />
        </div>
      </div>
    </div>
  );
};

export default AppointmentBooking;
