export interface Doctor {
  id: number;
  name: string;
  specialty: string;
  image: string;
  experience: string;
  rating: number;
  consultationFee: number;
}

export interface TimeSlot {
  id: number;
  doctorId: number;
  date: string;
  time: string;
  isBooked: boolean;
}

export interface Appointment {
  id: number;
  doctorId: number;
  patientName: string;
  email: string;
  phone: string;
  date: string;
  time: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  createdAt: string;
}

export interface AppointmentFormData {
  patientName: string;
  email: string;
  phone: string;
  date: string;
  time: string;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}
