import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { Doctor, TimeSlot, Appointment, AppointmentFormData } from '../types';

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: 'http://localhost:3001',
  }),
  tagTypes: ['Doctor', 'TimeSlot', 'Appointment'],
  endpoints: (builder) => ({
    // Get all doctors
    getDoctors: builder.query<Doctor[], void>({
      query: () => '/doctors',
      providesTags: ['Doctor'],
    }),

    // Get doctor by ID
    getDoctorById: builder.query<Doctor, number>({
      query: (id) => `/doctors/${id}`,
      providesTags: ['Doctor'],
    }),

    // Get time slots for a doctor
    getTimeSlots: builder.query<TimeSlot[], { doctorId: number; date?: string }>({
      query: ({ doctorId, date }) => {
        let url = `/timeSlots?doctorId=${doctorId}`;
        if (date) {
          url += `&date=${date}`;
        }
        return url;
      },
      providesTags: ['TimeSlot'],
    }),

    // Get appointments for a doctor
    getAppointments: builder.query<Appointment[], number>({
      query: (doctorId) => `/appointments?doctorId=${doctorId}`,
      providesTags: ['Appointment'],
    }),

    // Create new appointment
    createAppointment: builder.mutation<Appointment, AppointmentFormData & { doctorId: number }>({
      query: (appointmentData) => ({
        url: '/appointments',
        method: 'POST',
        body: {
          ...appointmentData,
          status: 'confirmed',
          createdAt: new Date().toISOString(),
        },
      }),
      invalidatesTags: ['Appointment', 'TimeSlot'],
    }),

    // Update time slot booking status
    updateTimeSlot: builder.mutation<TimeSlot, { id: number; isBooked: boolean }>({
      query: ({ id, isBooked }) => ({
        url: `/timeSlots/${id}`,
        method: 'PATCH',
        body: { isBooked },
      }),
      invalidatesTags: ['TimeSlot'],
    }),
  }),
});

export const {
  useGetDoctorsQuery,
  useGetDoctorByIdQuery,
  useGetTimeSlotsQuery,
  useGetAppointmentsQuery,
  useCreateAppointmentMutation,
  useUpdateTimeSlotMutation,
} = apiSlice;
