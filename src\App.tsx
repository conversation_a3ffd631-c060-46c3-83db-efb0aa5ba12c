import { useState } from 'react';
import { Provider } from 'react-redux';
import { store } from './store';
import DoctorSele<PERSON> from './pages/DoctorSelection';
import AppointmentBooking from './pages/AppointmentBooking';
import { Doctor } from './types';

function App() {
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);
  const [currentStep, setCurrentStep] = useState<'doctor-selection' | 'appointment-booking'>('doctor-selection');

  const handleDoctorSelect = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setCurrentStep('appointment-booking');
  };

  const handleBackToSelection = () => {
    setSelectedDoctor(null);
    setCurrentStep('doctor-selection');
  };

  return (
    <Provider store={store}>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <h1 className="text-3xl font-bold text-gray-900">
                Doctor Consultation
              </h1>
              {selectedDoctor && (
                <button
                  onClick={handleBackToSelection}
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  ← Back to Doctors
                </button>
              )}
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {currentStep === 'doctor-selection' && (
            <DoctorSelection onDoctorSelect={handleDoctorSelect} />
          )}
          {currentStep === 'appointment-booking' && selectedDoctor && (
            <AppointmentBooking doctor={selectedDoctor} />
          )}
        </main>
      </div>
    </Provider>
  );
}

export default App;
