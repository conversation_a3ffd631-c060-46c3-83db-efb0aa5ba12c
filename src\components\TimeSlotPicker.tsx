import React, { useState } from 'react';
import { TimeSlot } from '../types';

interface TimeSlotPickerProps {
  timeSlots: TimeSlot[];
  selectedDate: string;
  selectedTime: string;
  onDateChange: (date: string) => void;
  onTimeChange: (time: string) => void;
}

const TimeSlotPicker: React.FC<TimeSlotPickerProps> = ({
  timeSlots,
  selectedDate,
  selectedTime,
  onDateChange,
  onTimeChange,
}) => {
  // Get unique dates from time slots
  const availableDates = [...new Set(timeSlots.map(slot => slot.date))].sort();
  
  // Get time slots for selected date
  const timeSlotsForDate = timeSlots.filter(slot => slot.date === selectedDate);

  return (
    <div className="space-y-6">
      {/* Date Picker */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Date
        </label>
        <div className="grid grid-cols-3 gap-2">
          {availableDates.map((date) => (
            <button
              key={date}
              type="button"
              onClick={() => onDateChange(date)}
              className={`p-3 text-sm rounded-lg border transition-colors ${
                selectedDate === date
                  ? 'bg-primary-500 text-white border-primary-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              {new Date(date).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
              })}
            </button>
          ))}
        </div>
      </div>

      {/* Time Picker */}
      {selectedDate && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Time
          </label>
          <div className="grid grid-cols-4 gap-2">
            {timeSlotsForDate.map((slot) => (
              <button
                key={slot.id}
                type="button"
                onClick={() => !slot.isBooked && onTimeChange(slot.time)}
                disabled={slot.isBooked}
                className={`p-3 text-sm rounded-lg border transition-colors ${
                  slot.isBooked
                    ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                    : selectedTime === slot.time
                    ? 'bg-primary-500 text-white border-primary-500'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {slot.time}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TimeSlotPicker;
