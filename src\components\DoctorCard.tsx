import React from 'react';
import { Doctor } from '../types';

interface Doctor<PERSON>ard<PERSON><PERSON> {
  doctor: Doctor;
  onSelect: (doctor: Doctor) => void;
  isSelected?: boolean;
}

const DoctorCard: React.FC<DoctorCardProps> = ({ doctor, onSelect, isSelected }) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-md p-6 cursor-pointer transition-all duration-200 hover:shadow-lg ${
        isSelected ? 'ring-2 ring-primary-500 bg-primary-50' : ''
      }`}
      onClick={() => onSelect(doctor)}
    >
      <div className="flex items-center space-x-4">
        <img
          src={doctor.image}
          alt={doctor.name}
          className="w-16 h-16 rounded-full object-cover"
        />
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{doctor.name}</h3>
          <p className="text-sm text-gray-600">{doctor.specialty}</p>
          <div className="flex items-center space-x-4 mt-2">
            <span className="text-sm text-gray-500">{doctor.experience}</span>
            <div className="flex items-center">
              <span className="text-yellow-400">★</span>
              <span className="text-sm text-gray-600 ml-1">{doctor.rating}</span>
            </div>
          </div>
        </div>
        <div className="text-right">
          <p className="text-lg font-semibold text-primary-600">${doctor.consultationFee}</p>
          <p className="text-sm text-gray-500">Consultation</p>
        </div>
      </div>
    </div>
  );
};

export default DoctorCard;
